import asyncio
import base64
import datetime
import hashlib
import http
import io
import json
import logging
import os
import re
import shutil
import ssl
import subprocess
import tempfile
import urllib
from dataclasses import dataclass
from functools import cached_property
from pathlib import Path
from typing import Dict, Optional, Union
from urllib.parse import urlsplit

import attrs
import httpx
import pikepdf
import pylightxl
import redis
import requests
from Crypto.Cipher import AES
from dateutil import tz
from utensils.zip import ZipFilePlus, read_zip_first_file

from headstream import config
from headstream.config import get_config, project_root
from headstream.items import HKEXItem
from headstream.rule import RULES_MAP
from headstream.settings import USER_AGENT
from headstream.spiders import HKEXNEWS_URL, STOCK_ID_PATH, Category
from headstream.storing import filepath

ssl._create_default_https_context = ssl._create_unverified_context
_rdb = {}
logger = logging.getLogger(__name__)


class NotFoundException(Exception):
    pass


class DownloadError(Exception):
    pass


class InvalidContent(Exception):
    pass


class RedirectToDiffHost(Exception):
    pass


@attrs.define(slots=False)
class DocMeta:
    stock_code: str
    stock_name: str
    file_name: str
    headline: str
    release_time: int
    url: str
    ext: str = attrs.field(default="")
    stock_codes: str = attrs.field(default="")
    rule: str = attrs.field(default="")
    size: int = attrs.field(default=0, converter=int)
    hash: str = attrs.field(default="")
    fail_count: int = attrs.field(default=0, converter=int)
    ident: str = attrs.field(default="")

    def __attrs_post_init__(self):
        if not self.ext:
            self.ext = os.path.splitext(self.url)[1].lstrip(".").lower()
        if self.rule == "ESG" and "Annual Report" in self.headline:
            # ESG 章节在年报里面，算年报
            self.rule = "ar"

    @property
    def is_esg(self):
        # ESG 有两种情况：
        # 1. 独立的 ESG 报告
        # 2. 年报中的 ESG 章节
        #   2.1 年报的 headline 里有 ESG 关键字
        #   2.2 章节标题有 Environmental, Social and Governance Information/Report 字样（在Scriber解析过程中处理）
        return self.rule == "ESG" or (
            self.rule == "ar"
            and "Environmental, Social and Governance Information/Report"
            in self.headline
        )

    @property
    def page_limit(self) -> Optional[int]:
        return {
            "Q1": 5,
            "Interim": 5,
            "Q3": 5,
            "Final": 5,
            "ar": 30,
            "ESG": 5,
            "AGM": 5,
            # "POLL": 5,
        }.get(self.rule)

    @cached_property
    def release_date(self) -> datetime.datetime:
        return datetime.datetime.fromtimestamp(int(self.release_time))

    @property
    def pr_md5(self) -> str:
        return hashlib.md5(f"{urlsplit(self.url).path}{self.rule}".encode()).hexdigest()

    def to_scrapy_item(self) -> HKEXItem:
        item = HKEXItem()
        item["stock_codes"] = self.stock_codes
        item["stock_code"] = self.stock_code
        item["stock_name"] = self.stock_name
        item["headline"] = self.headline
        item["file_name"] = self.file_name
        item["ext"] = self.ext
        item["release_time"] = self.release_time
        item["url"] = self.url
        item["size"] = self.size
        item["hash"] = self.hash
        item["rule"] = self.rule
        return item

async def combin_pdf_from_htm(url: str, saved_path: str = None):
    """合并指定HKEX htm页面多页PDF为一个PDF"""
    p_href = re.compile(r'href="(.*?\.pdf)"')

    saved_path = saved_path or f"{Path(url).stem}.pdf"
    with tempfile.TemporaryDirectory(delete=False) as tmp_dir:
        work_dir = Path(tmp_dir)
        h5_path = work_dir / os.path.basename(url)
        await stream_download(url, h5_path)
        for i, m in enumerate(p_href.finditer(h5_path.read_text()), 1):
            logger.info(f"Downloading {m.group(1)}")
            logger.info(f"Downloading {os.path.join(os.path.dirname(url), m.group(1))}")
            await stream_download(
                os.path.join(os.path.dirname(url), m.group(1)),
                work_dir / f"{i:05d}.pdf",
            )
        with pikepdf.Pdf.new() as pdf:
            for path in sorted(work_dir.glob("*.pdf"), key=lambda x: x.name):
                with pikepdf.Pdf.open(path) as src:
                    pdf.pages.extend(src.pages)
            pdf.save(saved_path, linearize=True)


@dataclass
class DownloadWrapper:
    meta_info: DocMeta
    http_proxy: str
    lftp_bin = shutil.which("lftp") or None

    @classmethod
    def cmd(cls, cmd_list, out_path, http_proxy):
        if http_proxy:
            env = dict(os.environ, http_proxy=http_proxy, https_proxy=http_proxy)
        else:
            env = dict(os.environ)
        try:
            # pylint: disable=unexpected-keyword-arg
            logger.info(
                f"Running command: {cmd_list[-1].rsplit(';', maxsplit=2)[-2].strip()}"
            )
            ret_code = subprocess.Popen(
                cmd_list, env=env, encoding="utf-8", stdout=subprocess.DEVNULL
            ).wait(timeout=300)
        except subprocess.CalledProcessError as e:
            raise DownloadError(e)
        except subprocess.TimeoutExpired:
            raise DownloadError("Command timed out after 300 seconds")
        except Exception as e:
            logger.exception(e)
            raise DownloadError(f"Unexpected error: {e}")

        if ret_code != 0:
            raise DownloadError(
                f"Error while downloading file: {out_path}, exit status code: {ret_code}"
            )

    def build_in_func(self, dst_path):
        """Use build in func: urllib.request.urlretrieve download file(default)"""
        # pylint: disable=protected-access
        logging.warning(
            'No "lftp" binary found on your system, will use native python download func.'
        )
        ssl._create_default_https_context = ssl._create_unverified_context
        if self.http_proxy:
            # create the object, assign it to a variable
            proxy = urllib.request.ProxyHandler({"http": self.http_proxy})
            # construct a new opener using your proxy settings
            opener = urllib.request.build_opener(proxy)
            # install the opener on the module-level
            urllib.request.install_opener(opener)
        try:
            urllib.request.urlretrieve(self.meta_info.url, dst_path)
        except Exception as e:
            raise DownloadError(e)

    def run(self):
        """Download file over bad connection network env with lftp"""
        with tempfile.NamedTemporaryFile(buffering=0) as tmp_file:
            if self.lftp_bin:
                self.lftp(tmp_file.name)
            else:
                self.build_in_func(tmp_file.name)
            if (ins := RULES_MAP.get(self.meta_info.rule)) and ins.has_valid_content(
                tmp_file.name
            ):
                return tmp_file.read()
            raise InvalidContent(f"No valid content found: {self.meta_info}")

    def lftp(self, dst_path):
        arg_list = [
            self.lftp_bin,
            "-e",
            f"""
            set ssl:verify-certificate false;
            set xfer:max-redirections 5;
            set net:idle 10;
            set net:max-retries 5;
            set net:reconnect-interval-base 3;
            set net:reconnect-interval-max 3;
            set http:user-agent '{USER_AGENT}';
            pget -n 10 -c {self.meta_info.url} -o {dst_path};
            exit""",
        ]
        self.cmd(arg_list, dst_path, self.http_proxy)
        # asyncio.run(stream_download(self.url, dst_path))


def get_mode(spider):
    modes = ["full", "ifull", "incrementally"]
    attr = spider.settings.attributes.get("MODE")
    value = attr.value if attr else "full"
    return value if value in modes else modes[0]


def json_loads(data, default=None):
    try:
        return json.loads(data)
    except:
        if default is not None:
            return default
        raise


def md5sum(abs_path):
    hash_md5 = hashlib.md5()
    with open(abs_path, "rb") as file_obj:
        for chunk in iter(lambda: file_obj.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()


def push2scriber(url, item):
    """推送外部文档至Scriber接口"""
    if not url or not item["size"]:
        return

    scheme = "" if url.startswith("http") else get_config("scriber.scheme") + "://"
    url = "{}{}/{}".format(scheme, url.rstrip("/"), get_config("scriber.api"))
    dst_urls = set(get_config("dst_urls", []) + [url])
    meta = {
        "stock_code": item.get("stock_code", ""),
        "stock_codes": item.get("stock_codes", ""),
        "company": item.get("stock_name", ""),
        "filename": item["file_name"],
        "release_time": item["release_time"],
        "url": item["url"],
        "headline": item["headline"],
        "rule": item["rule"],
        "size": item["size"],
        "hash": item["hash"],
    }
    file_dir = os.path.join(project_root, "data", "hkex_ext_files")
    file_path = os.path.join(file_dir, item["hash"][:2], item["hash"][2:])
    try:
        with open(file_path, "rb") as file_obj:
            for url in dst_urls:
                logger.info(f"prepare to push data {meta} to {url}")
                rsp = requests.post(url, data=meta, files={"file": file_obj})
                if rsp.status_code == 200 and rsp.json().get("status") == "ok":
                    logging.info(rsp.text)
                else:
                    # 推送失败，删除redis中用来去重的url path+rule的md5值
                    # 下一轮爬虫会重新爬取该文件，直到推送成功
                    rdb = init_rdb()
                    rdb.srem("hkex:url:set", DocMeta(**item).pr_md5)
                    logging.error(
                        "failed to push file: %s, msg: %s" % (item["url"], rsp.text)
                    )
                    mm_notify(
                        f"failed to push file: {item}, msg: {rsp.text} @wangyangbin",
                        error=True,
                        tags=("hkex_spider",),
                    )
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        return



def is_exists(url):
    """指定链接是否可用"""
    try:
        with requests.head(url, timeout=(3, 5), verify=False) as req:
            return req.status_code == 200
    except Exception as e:
        logging.error(e)
        return False


def get_real_url(url):
    """获取指定url最终真实地址"""
    logging.info("get_real_url from %s", url)
    split_res = urllib.parse.urlsplit(url)
    host = f"{split_res.scheme}://{split_res.netloc}"
    for _ in range(5):
        rsp = requests.head(url, allow_redirects=False)
        if rsp.is_redirect:
            url = rsp.headers.get("location") or rsp.headers.get("Location")
            url_split = urllib.parse.urlsplit(url)
            if not url_split.netloc:
                url = urllib.parse.urljoin(host, url)
            elif url_split.netloc != split_res.netloc:
                raise RedirectToDiffHost
        elif rsp.status_code == 200:
            return url
        else:
            raise NotFoundException
    raise NotFoundException


def aes_encrypt(plaintext, key, fill=False, test=True):
    if test:
        passed = aes_encrypt_test()
        if not passed:
            exit(1)
    else:
        passed = True
    key = key.encode("utf8")
    blocksize = 16
    reminder_len = len(plaintext) % blocksize
    reminder = b""
    if reminder_len > 0:
        if fill:
            plaintext += b"\0" * (blocksize - reminder_len)
        else:
            plaintext, reminder = plaintext[:-reminder_len], plaintext[-reminder_len:]
    aes = AES.new(key, AES.MODE_CBC, key[11:27])
    return aes.encrypt(plaintext) + reminder if passed else b""


def aes_encrypt_test():
    key = "#hello-aes-hello-aes-hello-aes##"
    plaintext = b"this is plaintext."
    expectedtext = "B2tf/oy8SGl1PWonFpku3aEMoGl1q4Uw24B0VX96L24=\n"
    ciphertext = base64.encodebytes(
        aes_encrypt(plaintext, key=key, fill=True, test=False)
    )
    return expectedtext == ciphertext.decode()


def aes_decrypt(ciphertext, key, strip=False):
    key = key.encode("utf8")
    blocksize = 16
    reminder_len = len(ciphertext) % blocksize
    if not strip and reminder_len > 0:
        ciphertext, reminder = ciphertext[:-reminder_len], ciphertext[-reminder_len:]
    else:
        reminder = b""
    aes = AES.new(key, AES.MODE_CBC, key[11:27])

    if strip:
        return aes.decrypt(ciphertext).rstrip(b"\0")
    else:
        return aes.decrypt(ciphertext) + reminder


def init_rdb():
    pid = os.getpid()
    rdb = _rdb.get(pid)
    if not rdb:
        pool = redis.ConnectionPool(
            host=config.get_config("redis.host"),
            port=config.get_config("redis.port"),
            db=config.get_config("redis.db"),
            password=config.get_config("redis.password", ""),
            decode_responses=True,
        )
        rdb = redis.StrictRedis(connection_pool=pool)
        _rdb[pid] = rdb

    return rdb


def parse_date_from_link(link):
    """
    :param link: "https://www1.hkexnews.hk/listedco/listconews/sehk/2019/0716/ltn20190716103.pdf"
    :return: datetime obj or None
    """
    basename = os.path.basename(link)
    match = re.search(r"2\d{7}", basename)
    if match:
        date_str = match.group(0)
        try:
            date = datetime.datetime.strptime(date_str, "%Y%m%d")
        except ValueError:
            return None
        else:
            return date
    return None


def mm_notify(msg, error=False, force=False, tags: Optional[tuple] = None):
    channel = get_config("notification.channel", "hkex_exception")
    mm_hook_url = get_config(
        "notification.mattermost",
        "https://mm.paodingai.com/hooks/zxg3ncokc3yuxfymyrco7zctta",
    )
    if not force and not get_config("notification.switch"):
        return False
    icon = ":x:" if error else ":white_check_mark:"
    data = {
        "text": "👉#Scriber-{}-{}{}{}\n{}\n{}".format(
            os.environ.get("ENV", "dev"),
            os.uname()[1].replace(".", "_"),
            f" {' '.join('#' + t.replace(' ', '').ljust(3, '丨') for t in tags)} "
            if tags
            else " ",
            icon,
            msg,
            get_config("notification.tail", ""),
        ),
        "channel": channel,
        "username": "Scriber",
        "icon_url": "https://res.cloudinary.com/kdr2/image/upload/"
        "c_crop,g_faces,h_240,w_240/v1454772214/misc/c3p0-001.jpg",
    }
    try:
        rsp = requests.post(mm_hook_url, json=data)
        return rsp.status_code // 200 == 1
    except Exception:  # pylint: disable=broad-except
        return False


def utc2local(date: datetime.datetime) -> datetime.datetime:
    return date.replace(tzinfo=tz.tzutc()).astimezone(tz.tzlocal())


def local2utc(date: datetime.datetime) -> datetime.datetime:
    return date.replace(tzinfo=tz.tzlocal()).astimezone(tz.tzutc())


async def stream_download(url: str, save_path: Union[str, Path], chunk_size=io.DEFAULT_BUFFER_SIZE):
    logger.info(f'Download file from "{url}"')
    headers = {"user-agent": USER_AGENT}
    if isinstance(save_path, str):
        save_path = Path(save_path)
    if not save_path.parent.exists():
        save_path.parent.mkdir(parents=True, exist_ok=True)
    transport = httpx.AsyncHTTPTransport(verify=False, retries=3)
    async with httpx.AsyncClient(
        transport=transport, headers=headers, timeout=httpx.Timeout(timeout=60.0)
    ) as client:
        async with client.stream("GET", url) as rsp:
            rsp.raise_for_status()
            with open(save_path, "wb") as file_obj:
                async for chunk in rsp.aiter_bytes(chunk_size=chunk_size):
                    file_obj.write(chunk)
    logger.info(f'Download complete, file saved to "{save_path}"')


def get_latest_listing_file() -> str:
    file_dir = filepath("hkex_ext_files", "lists", "")
    files = os.listdir(file_dir)
    if not files:
        logger.warning("No listing file found")
        return ""
    files.sort(reverse=True)
    return os.path.join(file_dir, files[0])


def load_valid_stocks(path=None) -> Dict[str, str]:
    stocks = {}
    valid_companies = (
        "equity securities (main board)",
        "equity securities (gem)",
        "investment companies",
    )
    db = pylightxl.readxl(path or get_latest_listing_file())
    stock_idx = 0
    category_idx = 3
    ccass_idx = 14
    for row in db.ws(db.ws_names[0]).rows:
        if str(row[0]).strip().lower() == "stock code":
            for idx, cell in enumerate(row):
                if cell.strip().lower() == "sub-category":
                    category_idx = idx
                elif cell.strip().lower() == "admitted to ccass":
                    ccass_idx = idx
                    break
            continue
        if not all(
            isinstance(row[i], str) for i in (stock_idx, category_idx, ccass_idx)
        ):
            continue
        if (
            row[stock_idx].strip().isdigit()
            and row[category_idx].strip().lower() in valid_companies
            and row[ccass_idx].strip().lower() == "y"
        ):
            stocks[row[stock_idx].strip().rjust(5, "0")] = row[1].strip()
    assert stocks, "no valid stocks found"
    return stocks


def find_delisted_codes(previous_path: str, new_path: str) -> Dict[str, str]:
    """比较两个listing文件，找出已退市的股票代码"""
    previous_stocks = load_valid_stocks(previous_path)
    new_stocks = load_valid_stocks(new_path)
    res = {
        code: name for code, name in previous_stocks.items() if code not in new_stocks
    }
    return res

async def find_and_push_delisted_codes(previous_path, now_path, url=""):
    if delisted_codes := find_delisted_codes(previous_path, now_path):
        logger.info(f"Found delisted codes: {delisted_codes}, pushing...")
        transport = httpx.AsyncHTTPTransport(verify=False, retries=3)
        async with httpx.AsyncClient(
            transport=transport,
            headers={"user-agent": USER_AGENT},
            timeout=httpx.Timeout(timeout=10.0),
        ) as client:
            url = (
                url
                or f"{get_config('scriber.scheme')}://{get_config('scriber.netloc')}/{get_config('scriber.api')}"
            )
            await client.post(
                url,
                data={
                    "rule": "delisted_stocks",
                    "stock_codes": json.dumps(delisted_codes),
                },
                files={"file": b""},
            )
    else:
        logger.info("No delisted codes found")


async def code2id(stock_code: str, is_en=True, category=Category.CURRENT, retry=3):
    """stock code转为stockId"""
    url = f"{HKEXNEWS_URL}/search/partial.do"
    params = {
        "callback": "callback",
        "lang": "EN" if is_en else "ZH",
        "type": "A" if category == Category.CURRENT else "I",
        "name": stock_code,
        "market": "SEHK",
        "_": int(datetime.datetime.now().timestamp() * 1000),
    }

    async with httpx.AsyncClient(
        transport=httpx.AsyncHTTPTransport(retries=3, verify=False),
        timeout=httpx.Timeout(timeout=10.0),
        headers={"user-agent": USER_AGENT},
    ) as client:
        try:
            rsp = await client.get(url, params=params)
        except Exception as e:
            logger.error(e)
        else:
            if rsp.status_code == http.HTTPStatus.OK:
                res = rsp.text.strip().lstrip("callback(").rstrip(");")
                for item in json.loads(res).get("stockInfo", []):
                    if item.get("code") == stock_code:
                        logger.info(
                            f"stock code: {stock_code} -> stockId: {item['stockId']}"
                        )
                        return str(item["stockId"])
                logger.warning(f"stock id for {stock_code} not found")
                return None
    await asyncio.sleep(1)
    return await code2id(stock_code, is_en, category, retry - 1) if retry else None


async def codes2id(*, stocks=None, concurrency=5):
    stocks = stocks or load_valid_stocks()
    semaphore = asyncio.Semaphore(concurrency)

    async def sem_code2id(stock):
        async with semaphore:
            return await code2id(stock)

    results = await asyncio.gather(*(sem_code2id(stock) for stock in stocks))
    return dict(zip(stocks, results))


async def update_stock_id(init=False):
    if not init and os.path.exists(STOCK_ID_PATH):
        # 读取已有的stock id，增量更新模式
        cached_codes = json.loads(read_zip_first_file(STOCK_ID_PATH))
    else:
        cached_codes = {}
    if codes := {k: v for k, v in cached_codes.items() if not v}:
        new_stocks = await codes2id(stocks={k: v for k, v in codes.items() if not v})
        cached_codes.update(new_stocks)
        with ZipFilePlus(STOCK_ID_PATH, mode="w") as zip_fp:
            zip_fp.writestr(
                "stock_id.json", json.dumps(cached_codes, ensure_ascii=False, indent=2)
            )
    else:
        logger.info("No stock codes to update")


if __name__ == "__main__":
    asyncio.run(update_stock_id())
