import asyncio
import datetime
import hashlib
import logging
import os

import attrs
import httpx
from billiard.exceptions import TimeLimitExceeded
from celery import Celery
from celery.schedules import crontab
from celery.signals import worker_ready, setup_logging
from pdfparser.pdftools.count_page_num import get_page_num
from scrapy.crawler import CrawlerProcess
from scrapy.utils.project import get_project_settings
from celery import current_task
from headstream.common import (
    init_rdb,
    DownloadWrapper,
    DownloadError,
    DocMeta,
    stream_download,
    get_latest_listing_file,
    find_and_push_delisted_codes,
    load_valid_stocks,
    mm_notify,
    InvalidContent,
    update_stock_id, find_delisted_codes,
)
from headstream.config import get_config
from headstream.pipelines import HKEXPipeline
from headstream.settings import USER_AGENT
from headstream.storing import save, filepath
from loguru import logger

app = Celery(get_config("worker.app_name"), broker=get_config("worker.broker"))
app.conf.update(
    timezone="Asia/Shanghai",
    enable_utc=True,
    task_reject_on_worker_lost=True,  # 当worker进程意外退出时，task会被放回到队列中
    task_acks_late=True,  # 当worker完成了这个task时，任务才被标记为ack状态
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1,  # 执行一次任务即销毁worker, 保证爬虫能正常启停
    broker_connection_retry_on_startup=True,  # 重启时重连
    task_routes={
        "headstream.tasks.crawl": {"queue": "crawl"},
        "headstream.tasks.download": {"queue": "download"},
        "headstream.tasks.fetch_stocks_file": {"queue": "download"},
    },
    broker_transport_options={"visibility_timeout": 360000},
    result_accept_content=["application/json", "json"],
    beat_schedule={
        # 按需定时运行爬虫任务
        # https://mm.paodingai.com/cheftin/pl/9o3ysjjrfpbamntyrnzgo9xecc
        "call-crawler-every-5-min": {
            "task": "headstream.tasks.crawl",
            "schedule": crontab(minute="*/5", hour="6-23"),
            "kwargs": dict(
                xls=True,
                ar=True,  # 允许爬取年报
                netloc="{}://{}".format(
                    get_config("scriber.scheme"), get_config("scriber.netloc")
                ),
            ),
        },
        "call-crawler-every-5-min1": {
            "task": "headstream.tasks.crawl",
            "schedule": crontab(minute="5-35/5", hour="23"),
            "kwargs": dict(
                xls=True,
                ar=True,  # 允许爬取年报
                netloc="{}://{}".format(
                    get_config("scriber.scheme"), get_config("scriber.netloc")
                ),
            ),
        },
        "call-crawler": {
            "task": "headstream.tasks.crawl",
            "schedule": crontab(minute=30, hour=2),
            "kwargs": dict(
                xls=True,
                ar=True,  # 允许爬取年报
                netloc="{}://{}".format(
                    get_config("scriber.scheme"), get_config("scriber.netloc")
                ),
            ),
        },
        "update_stocks_file": {
            "task": "headstream.tasks.fetch_stocks_file",
            "schedule": crontab(minute="0", hour="*/1"),
        },
    },
)


@worker_ready.connect
def _worker_ready(*args, **kwargs):
    logger.info(f"Worker ready: {os.getpid()}, {args}")


@setup_logging.connect
def _setup_logging(*args, **kwargs):
    logger.info(f"set logging level: {get_config('logging.level')}")


@app.task(
    autoretry_for=(TimeLimitExceeded,),
    retry_kwargs={"max_retries": 5},
    retry_backoff=True,
    time_limit=10 * 60,
)
def crawl(spider_name="hkex_ext", *args, **kwargs):
    logger.debug(f"Crawl task received: {args}, {kwargs}")
    process = CrawlerProcess(get_project_settings())
    process.crawl(spider_name, *args, **kwargs)
    process.start()


@app.task(
    autoretry_for=(DownloadError,),
    retry_kwargs={"max_retries": 5},
    retry_backoff=True,
    time_limit=10 * 60,
)
def download(spider_name, hash_key, push_url=None, use_db=False):
    db = init_rdb()
    item = db.hgetall(hash_key)
    if not item or not isinstance(item, dict):
        logger.error(f"Download task received invalid hash_key: {hash_key}")
        return
    item = DocMeta(**item)
    logger.info(f"Download task received: {item.rule} -> {item.url}")
    try:
        data = DownloadWrapper(item, http_proxy=get_config("app.proxy")).run()
    except DownloadError as exp:
        # 增加失败计数
        db.hincrby(hash_key, "fail_count")
        mm_notify(
            f"@wangyangbin download file error {item}, exception: {exp}",
            tags=("hkex_spider", "download error"),
        )
        logger.exception("lftp download fail, %s", item.file_name)
        raise exp
    except InvalidContent as exp:
        mm_notify(
            f"@wangyangbin download file has invalid content {item}, exception: {exp}",
            tags=("hkex_spider", "invalid content"),
        )
        logger.warning(exp)
        db.delete(hash_key)
        return
    except Exception as e:
        logger.exception(f"download file error {item}, exception: {e}")
        mm_notify(
            f"@wangyangbin download file error {item}, exception: {e}",
            tags=("hkex_spider", "unexpected"),
        )
        return

    try:
        if item.page_limit and get_page_num(data) <= item.page_limit:
            logger.warning(f"Few pages pdf file: {item}")
            db.delete(hash_key)
            return
    except Exception as e:
        logger.exception(f"Error when count page number: {item}, exception: {e}")
        db.delete(hash_key)
        return

    item.size = len(data)
    item.hash = hashlib.md5(data).hexdigest()

    db.hset(hash_key, mapping=attrs.asdict(item))

    if item.ident:
        ident = item.ident
        file_name = item.file_name
    else:
        ident = item.hash[:2]
        file_name = item.hash[2:]
    save(spider_name, ident, file_name, data)
    if current_task.request.id:
        logger.info(f"start write download record: {item.stock_code} | {item.rule}")
        for key in db.scan_iter(hash_key):
            try:
                file = DocMeta(**db.hgetall(key))
            except ValueError as exp:
                logger.warning(exp)
                db.delete(key)
                continue
            if file.size and file.hash:
                db.delete(key)
                HKEXPipeline(push_url=push_url, use_db=use_db).process_item(
                    file.to_scrapy_item()
                )
                continue

            if int(file.fail_count) > 5:
                # 失败次数超过5次，删除记录
                db.delete(key)
                db.redis.srem("hkex:url:set", key.rsplit(":", maxsplit=1)[-1])
                mm_notify(
                    f"{attrs.asdict(file)} @wangyangbin\nWaiting for next try.",
                    tags=("hkex_spider", "timeout_task"),
                )


@app.task(
    autoretry_for=(DownloadError,),
    retry_kwargs={"max_retries": 3},
    retry_backoff=True,
    time_limit=10 * 60,
)
def fetch_stocks_file():
    async def run() -> str:
        """下载文件"""
        await update_stock_id()
        url = "https://www.hkex.com.hk/eng/services/trading/securities/securitieslists/ListOfSecurities.xlsx"
        transport = httpx.AsyncHTTPTransport(verify=False, retries=3)
        async with httpx.AsyncClient(
            transport=transport,
            headers={"user-agent": USER_AGENT},
            timeout=httpx.Timeout(timeout=60.0),
        ) as client:
            rsp = await client.head(url)
            if httpx.codes.is_server_error(rsp.status_code):
                raise DownloadError(f"{rsp.status_code} {rsp.text}")
        last_modified = rsp.headers.get("Last-Modified")
        file_name = datetime.datetime.strptime(
            last_modified, "%a, %d %b %Y %H:%M:%S GMT"
        ).strftime("%Y-%m-%d.xlsx")
        save_path = filepath("hkex_ext_files", "lists", file_name)
        remote_length = int(rsp.headers.get("Content-Length", 0))
        if os.path.exists(save_path) and os.path.getsize(save_path) == remote_length:
            logger.info(f'File "{save_path}" already exists')
            return save_path

        try:
            await stream_download(url, save_path)
            load_valid_stocks(save_path)
        except Exception as e:
            logger.warning(f"Download file failed: {e}")
            mm_notify(
                f"白名单下载/解析失败，请注意检查\n\n```\n{e}```\n",
                error=True,
                tags=("hkex_spider",),
            )
            raise DownloadError(f"Download file failed: {save_path}")

        if not os.path.exists(save_path) or remote_length != os.path.getsize(save_path):
            raise DownloadError(f"Download file failed: {save_path}")

        if previous_path := get_latest_listing_file():
            await find_and_push_delisted_codes(previous_path, save_path)
        return save_path

    asyncio.run(run())

if __name__ == '__main__':

  find_delisted_codes(
        # "/Users/<USER>/PycharmProjects/headstream/data/hkex_ext_files/lists/2025-04-10.xlsx",
        # "/Users/<USER>/PycharmProjects/headstream/data/hkex_ext_files/lists/2025-05-29.xlsx",
        "/Users/<USER>/Downloads/2025-05-06.xlsx",
        "/Users/<USER>/Downloads/2025-05-07.xlsx",
    )
